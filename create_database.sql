-- Achieve CxO MySQL Database Setup
-- Execute this script in MySQL command line or phpMyAdmin

-- Create the database
CREATE DATABASE IF NOT EXISTS `achieve_cxo` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Use the database
USE `achieve_cxo`;

-- Create the cxo_members table
CREATE TABLE IF NOT EXISTS `cxo_members` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `first_name` varchar(100) NOT NULL,
    `last_name` varchar(100) NOT NULL,
    `email` varchar(255) NOT NULL,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `status` enum('pending','active','inactive') DEFAULT 'pending',
    PRIMARY KEY (`id`),
    <PERSON>IQ<PERSON> KEY `email` (`email`),
    INDEX `idx_created_at` (`created_at`),
    INDEX `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Verify table creation
SHOW TABLES;
DESCRIBE cxo_members;
