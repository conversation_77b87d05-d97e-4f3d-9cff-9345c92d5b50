<?php
session_start();

require_once '../config/database.php';

class AdminAuth {
    private $pdo;
    
    public function __construct() {
        $this->pdo = getDatabaseConnection();
    }
    
    public function login($username, $password) {
        try {
            // Get admin user
            $stmt = $this->pdo->prepare("SELECT id, username, email, password_hash, full_name, status FROM admin_users WHERE username = ? AND status = 'active'");
            $stmt->execute([$username]);
            $admin = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$admin) {
                return ['success' => false, 'message' => 'Invalid username or password'];
            }
            
            // Verify password
            if (!password_verify($password, $admin['password_hash'])) {
                return ['success' => false, 'message' => 'Invalid username or password'];
            }
            
            // Create session
            $sessionId = $this->createSession($admin['id']);
            
            // Update last login
            $stmt = $this->pdo->prepare("UPDATE admin_users SET last_login = NOW() WHERE id = ?");
            $stmt->execute([$admin['id']]);
            
            // Set session variables
            $_SESSION['admin_id'] = $admin['id'];
            $_SESSION['admin_username'] = $admin['username'];
            $_SESSION['admin_name'] = $admin['full_name'];
            $_SESSION['admin_session_id'] = $sessionId;
            
            // Log activity
            $this->logActivity($admin['id'], 'login', null, null, 'Admin logged in');
            
            return ['success' => true, 'message' => 'Login successful'];
            
        } catch (Exception $e) {
            error_log("Admin login error: " . $e->getMessage());
            return ['success' => false, 'message' => 'Login failed. Please try again.'];
        }
    }
    
    public function logout() {
        if (isset($_SESSION['admin_session_id'])) {
            // Remove session from database
            $stmt = $this->pdo->prepare("DELETE FROM admin_sessions WHERE id = ?");
            $stmt->execute([$_SESSION['admin_session_id']]);
        }
        
        // Log activity before destroying session
        if (isset($_SESSION['admin_id'])) {
            $this->logActivity($_SESSION['admin_id'], 'logout', null, null, 'Admin logged out');
        }
        
        // Destroy session
        session_destroy();
        return ['success' => true, 'message' => 'Logged out successfully'];
    }
    
    public function isLoggedIn() {
        if (!isset($_SESSION['admin_id']) || !isset($_SESSION['admin_session_id'])) {
            return false;
        }
        
        // Check if session exists and is valid
        $stmt = $this->pdo->prepare("SELECT id FROM admin_sessions WHERE id = ? AND admin_id = ? AND expires_at > NOW()");
        $stmt->execute([$_SESSION['admin_session_id'], $_SESSION['admin_id']]);
        
        return $stmt->fetch() !== false;
    }
    
    public function requireAuth() {
        if (!$this->isLoggedIn()) {
            $message = isset($_SESSION['admin_id']) ? 'session_expired' : 'unauthorized';
            header('Location: login.php?message=' . $message);
            exit;
        }
    }
    
    private function createSession($adminId) {
        $sessionId = bin2hex(random_bytes(32));
        $expiresAt = date('Y-m-d H:i:s', strtotime('+24 hours'));
        $ipAddress = $_SERVER['REMOTE_ADDR'] ?? null;
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? null;
        
        $stmt = $this->pdo->prepare("INSERT INTO admin_sessions (id, admin_id, expires_at, ip_address, user_agent) VALUES (?, ?, ?, ?, ?)");
        $stmt->execute([$sessionId, $adminId, $expiresAt, $ipAddress, $userAgent]);
        
        return $sessionId;
    }
    
    public function logActivity($adminId, $action, $targetType = null, $targetId = null, $details = null) {
        try {
            $ipAddress = $_SERVER['REMOTE_ADDR'] ?? null;
            $stmt = $this->pdo->prepare("INSERT INTO admin_activity_log (admin_id, action, target_type, target_id, details, ip_address) VALUES (?, ?, ?, ?, ?, ?)");
            $stmt->execute([$adminId, $action, $targetType, $targetId, $details, $ipAddress]);
        } catch (Exception $e) {
            error_log("Failed to log admin activity: " . $e->getMessage());
        }
    }
    
    public function cleanupExpiredSessions() {
        $stmt = $this->pdo->prepare("DELETE FROM admin_sessions WHERE expires_at < NOW()");
        $stmt->execute();
    }
}

// Initialize auth object
$adminAuth = new AdminAuth();

// Clean up expired sessions periodically
if (rand(1, 100) === 1) {
    $adminAuth->cleanupExpiredSessions();
}
?>
