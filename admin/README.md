# Achieve CxO Admin Panel

## Setup Instructions

### 1. Database Setup
First, run the admin database setup:

1. Navigate to `admin/setup_admin.php` in your browser
2. Click "Setup Database Tables" to create admin tables
3. Create your first admin user account

### 2. Default Admin Credentials
The setup script creates a default admin user:
- **Username:** admin
- **Password:** admin123
- **Email:** <EMAIL>

**⚠️ IMPORTANT:** Change these credentials immediately after setup!

### 3. Admin Panel Features

#### Dashboard Tabs:
- **Pending Approval:** Users waiting for approval/rejection
- **All Users:** Complete list of all registered users
- **Inactive Users:** Rejected or deactivated users

#### User Management Actions:
- **Approve:** Changes status from 'pending' to 'active'
- **Reject:** Changes status from 'pending' to 'inactive'
- **Reactivate:** Changes status from 'inactive' to 'active'
- **Deactivate:** Changes status from 'active' to 'inactive'

### 4. Security Features
- Session-based authentication
- Password hashing with <PERSON><PERSON>'s password_hash()
- Activity logging for all admin actions
- Session expiration (24 hours)
- CSRF protection through session validation

### 5. File Structure
```
admin/
├── auth.php              # Authentication class and session management
├── dashboard.php          # Main admin dashboard with tabs
├── login.php             # Admin login form
├── logout.php            # Logout handler
├── setup_admin.php       # Database setup and admin creation
├── update_user_status.php # AJAX endpoint for status updates
├── index.php             # Redirects to login
└── README.md             # This file

database/
└── admin_setup.sql       # SQL for admin tables
```

### 6. Database Tables Created
- `admin_users` - Admin user accounts
- `admin_sessions` - Session management
- `admin_activity_log` - Activity logging

### 7. Access
- **Public Site:** `index.html`
- **Admin Login:** `admin/login.php`
- **Admin Dashboard:** `admin/dashboard.php`

### 8. Status Flow
```
New User Registration → pending
Admin Approval → active
Admin Rejection → inactive
Reactivation → active
```

### 9. Future Enhancements
- Email notifications for status changes
- Bulk user actions
- Advanced filtering and search
- User profile editing
- Export functionality
- Admin user management
