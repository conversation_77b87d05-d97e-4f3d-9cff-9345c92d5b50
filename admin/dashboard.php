<?php
require_once 'auth.php';

// Require authentication
$adminAuth->requireAuth();

// Get user statistics
function getUserStats($pdo) {
    $stats = [];
    
    // Total users
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM cxo_members");
    $stats['total'] = $stmt->fetch()['total'];
    
    // Pending users
    $stmt = $pdo->query("SELECT COUNT(*) as pending FROM cxo_members WHERE status = 'pending'");
    $stats['pending'] = $stmt->fetch()['pending'];
    
    // Active users
    $stmt = $pdo->query("SELECT COUNT(*) as active FROM cxo_members WHERE status = 'active'");
    $stats['active'] = $stmt->fetch()['active'];
    
    // Inactive users
    $stmt = $pdo->query("SELECT COUNT(*) as inactive FROM cxo_members WHERE status = 'inactive'");
    $stats['inactive'] = $stmt->fetch()['inactive'];
    
    return $stats;
}

// Get users by status
function getUsersByStatus($pdo, $status = null, $limit = 50, $offset = 0) {
    if ($status) {
        $stmt = $pdo->prepare("SELECT * FROM cxo_members WHERE status = ? ORDER BY created_at DESC LIMIT ? OFFSET ?");
        $stmt->execute([$status, $limit, $offset]);
    } else {
        $stmt = $pdo->prepare("SELECT * FROM cxo_members ORDER BY created_at DESC LIMIT ? OFFSET ?");
        $stmt->execute([$limit, $offset]);
    }
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

$pdo = getDatabaseConnection();
$stats = getUserStats($pdo);

// Get current tab
$currentTab = $_GET['tab'] ?? 'pending';
$validTabs = ['pending', 'all', 'inactive'];
if (!in_array($currentTab, $validTabs)) {
    $currentTab = 'pending';
}

// Get users for current tab
switch ($currentTab) {
    case 'pending':
        $users = getUsersByStatus($pdo, 'pending');
        break;
    case 'inactive':
        $users = getUsersByStatus($pdo, 'inactive');
        break;
    default:
        $users = getUsersByStatus($pdo);
        break;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - Achieve CxO</title>
    <link rel="stylesheet" href="../css/bootstrap.min.css">
    <link rel="stylesheet" href="https://code.ionicframework.com/ionicons/2.0.1/css/ionicons.min.css">
    <style>
        body {
            background: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .navbar {
            background: linear-gradient(135deg, #aa00ff, #7200ca);
            padding: 1rem 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .navbar-brand {
            color: white !important;
            font-weight: 600;
            font-size: 1.5rem;
        }
        
        .navbar-nav .nav-link {
            color: rgba(255,255,255,0.9) !important;
            font-weight: 500;
        }
        
        .navbar-nav .nav-link:hover {
            color: white !important;
        }
        
        .dashboard-header {
            background: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .stats-card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 4px solid #aa00ff;
            margin-bottom: 1rem;
        }
        
        .stats-number {
            font-size: 2rem;
            font-weight: 700;
            color: #aa00ff;
            margin-bottom: 0.5rem;
        }
        
        .stats-label {
            color: #6c757d;
            font-weight: 500;
            text-transform: uppercase;
            font-size: 0.875rem;
        }
        
        .nav-tabs {
            border-bottom: 2px solid #e9ecef;
            margin-bottom: 2rem;
        }
        
        .nav-tabs .nav-link {
            border: none;
            color: #6c757d;
            font-weight: 500;
            padding: 1rem 1.5rem;
            border-radius: 0;
        }
        
        .nav-tabs .nav-link.active {
            background: none;
            color: #aa00ff;
            border-bottom: 2px solid #aa00ff;
        }
        
        .nav-tabs .nav-link:hover {
            border-color: transparent;
            color: #aa00ff;
        }
        
        .users-table {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .table {
            margin-bottom: 0;
        }
        
        .table th {
            background: #f8f9fa;
            border-top: none;
            font-weight: 600;
            color: #495057;
            padding: 1rem;
        }
        
        .table td {
            padding: 1rem;
            vertical-align: middle;
        }
        
        .status-badge {
            padding: 0.375rem 0.75rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 500;
        }
        
        .status-pending {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-active {
            background: #d4edda;
            color: #155724;
        }
        
        .status-inactive {
            background: #f8d7da;
            color: #721c24;
        }
        
        .action-btn {
            padding: 0.375rem 0.75rem;
            border: none;
            border-radius: 6px;
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            margin: 0 0.25rem;
            transition: all 0.3s ease;
        }
        
        .btn-approve {
            background: #28a745;
            color: white;
        }
        
        .btn-approve:hover {
            background: #218838;
            transform: translateY(-1px);
        }
        
        .btn-reject {
            background: #dc3545;
            color: white;
        }
        
        .btn-reject:hover {
            background: #c82333;
            transform: translateY(-1px);
        }
        
        .btn-reactivate {
            background: #007bff;
            color: white;
        }
        
        .btn-reactivate:hover {
            background: #0056b3;
            transform: translateY(-1px);
        }
        
        .empty-state {
            text-align: center;
            padding: 3rem;
            color: #6c757d;
        }
        
        .empty-state i {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg">
        <div class="container">
            <a class="navbar-brand" href="dashboard.php">
                <i class="ion-ios-people"></i> Achieve CxO Admin
            </a>
            <div class="navbar-nav ms-auto">
                <span class="nav-link">Welcome, <?php echo htmlspecialchars($_SESSION['admin_name']); ?></span>
                <a class="nav-link" href="logout.php">
                    <i class="ion-log-out"></i> Logout
                </a>
            </div>
        </div>
    </nav>
    
    <div class="dashboard-header">
        <div class="container">
            <h1>Dashboard</h1>
            <p class="text-muted">Manage user applications and memberships</p>
        </div>
    </div>
    
    <div class="container">
        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="stats-number"><?php echo $stats['total']; ?></div>
                    <div class="stats-label">Total Users</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="stats-number"><?php echo $stats['pending']; ?></div>
                    <div class="stats-label">Pending Approval</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="stats-number"><?php echo $stats['active']; ?></div>
                    <div class="stats-label">Active Members</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="stats-number"><?php echo $stats['inactive']; ?></div>
                    <div class="stats-label">Inactive Users</div>
                </div>
            </div>
        </div>
        
        <!-- Navigation Tabs -->
        <ul class="nav nav-tabs">
            <li class="nav-item">
                <a class="nav-link <?php echo $currentTab === 'pending' ? 'active' : ''; ?>" 
                   href="?tab=pending">
                    <i class="ion-clock"></i> Pending Approval (<?php echo $stats['pending']; ?>)
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo $currentTab === 'all' ? 'active' : ''; ?>" 
                   href="?tab=all">
                    <i class="ion-people"></i> All Users (<?php echo $stats['total']; ?>)
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo $currentTab === 'inactive' ? 'active' : ''; ?>" 
                   href="?tab=inactive">
                    <i class="ion-close-circled"></i> Inactive Users (<?php echo $stats['inactive']; ?>)
                </a>
            </li>
        </ul>
        
        <!-- Users Table -->
        <div class="users-table">
            <?php if (empty($users)): ?>
                <div class="empty-state">
                    <i class="ion-person"></i>
                    <h4>No users found</h4>
                    <p>There are no users in this category yet.</p>
                </div>
            <?php else: ?>
                <table class="table">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Status</th>
                            <th>Joined</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($users as $user): ?>
                            <tr>
                                <td>
                                    <strong><?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?></strong>
                                </td>
                                <td><?php echo htmlspecialchars($user['email']); ?></td>
                                <td>
                                    <span class="status-badge status-<?php echo $user['status']; ?>">
                                        <?php echo ucfirst($user['status']); ?>
                                    </span>
                                </td>
                                <td><?php echo date('M j, Y', strtotime($user['created_at'])); ?></td>
                                <td>
                                    <?php if ($user['status'] === 'pending'): ?>
                                        <button class="action-btn btn-approve" onclick="updateUserStatus(<?php echo $user['id']; ?>, 'active')">
                                            <i class="ion-checkmark"></i> Approve
                                        </button>
                                        <button class="action-btn btn-reject" onclick="updateUserStatus(<?php echo $user['id']; ?>, 'inactive')">
                                            <i class="ion-close"></i> Reject
                                        </button>
                                    <?php elseif ($user['status'] === 'inactive'): ?>
                                        <button class="action-btn btn-reactivate" onclick="updateUserStatus(<?php echo $user['id']; ?>, 'active')">
                                            <i class="ion-refresh"></i> Reactivate
                                        </button>
                                    <?php elseif ($user['status'] === 'active'): ?>
                                        <button class="action-btn btn-reject" onclick="updateUserStatus(<?php echo $user['id']; ?>, 'inactive')">
                                            <i class="ion-close"></i> Deactivate
                                        </button>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php endif; ?>
        </div>
    </div>
    
    <script>
        function updateUserStatus(userId, newStatus) {
            const actionText = newStatus === 'active' ? 'approve' : 
                              newStatus === 'inactive' ? 'reject' : 'update';
            
            if (!confirm(`Are you sure you want to ${actionText} this user?`)) {
                return;
            }
            
            fetch('update_user_status.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    user_id: userId,
                    status: newStatus
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred. Please try again.');
            });
        }
    </script>
</body>
</html>
