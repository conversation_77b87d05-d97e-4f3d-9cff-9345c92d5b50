<?php
/**
 * Admin Setup Script
 * Run this once to set up admin functionality
 */

require_once '../config/database.php';

function setupAdminTables() {
    try {
        $pdo = getDatabaseConnection();
        if (!$pdo) {
            throw new Exception('Database connection failed');
        }
        
        // Read and execute admin setup SQL
        $sql = file_get_contents('../database/admin_setup.sql');
        if (!$sql) {
            throw new Exception('Could not read admin_setup.sql file');
        }
        
        // Split SQL into individual statements
        $statements = array_filter(array_map('trim', explode(';', $sql)));
        
        $pdo->beginTransaction();
        
        foreach ($statements as $statement) {
            if (!empty($statement) && !preg_match('/^--/', $statement)) {
                $pdo->exec($statement);
            }
        }
        
        $pdo->commit();
        
        return ['success' => true, 'message' => 'Admin tables created successfully'];
        
    } catch (Exception $e) {
        if (isset($pdo)) {
            $pdo->rollBack();
        }
        error_log("Admin setup error: " . $e->getMessage());
        return ['success' => false, 'message' => 'Setup failed: ' . $e->getMessage()];
    }
}

function createAdminUser($username, $email, $password, $fullName) {
    try {
        $pdo = getDatabaseConnection();
        if (!$pdo) {
            throw new Exception('Database connection failed');
        }
        
        // Check if admin already exists
        $stmt = $pdo->prepare("SELECT id FROM admin_users WHERE username = ? OR email = ?");
        $stmt->execute([$username, $email]);
        if ($stmt->fetch()) {
            return ['success' => false, 'message' => 'Admin user already exists'];
        }
        
        // Create password hash
        $passwordHash = password_hash($password, PASSWORD_DEFAULT);
        
        // Insert admin user
        $stmt = $pdo->prepare("INSERT INTO admin_users (username, email, password_hash, full_name) VALUES (?, ?, ?, ?)");
        $stmt->execute([$username, $email, $passwordHash, $fullName]);
        
        return ['success' => true, 'message' => 'Admin user created successfully'];
        
    } catch (Exception $e) {
        error_log("Create admin user error: " . $e->getMessage());
        return ['success' => false, 'message' => 'Failed to create admin user: ' . $e->getMessage()];
    }
}

// Handle form submission
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'setup_tables') {
        $result = setupAdminTables();
        $message = $result['message'];
        $messageType = $result['success'] ? 'success' : 'error';
    } elseif ($action === 'create_admin') {
        $username = trim($_POST['username'] ?? '');
        $email = trim($_POST['email'] ?? '');
        $password = $_POST['password'] ?? '';
        $fullName = trim($_POST['full_name'] ?? '');
        
        if (empty($username) || empty($email) || empty($password) || empty($fullName)) {
            $message = 'All fields are required';
            $messageType = 'error';
        } elseif (strlen($password) < 6) {
            $message = 'Password must be at least 6 characters';
            $messageType = 'error';
        } else {
            $result = createAdminUser($username, $email, $password, $fullName);
            $message = $result['message'];
            $messageType = $result['success'] ? 'success' : 'error';
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Setup - Achieve CxO</title>
    <link rel="stylesheet" href="../css/bootstrap.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem 0;
        }
        
        .setup-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 3rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-width: 600px;
            margin: 0 auto;
        }
        
        .setup-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .setup-header h1 {
            color: #aa00ff;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        
        .alert {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .setup-section {
            background: #f8f9fa;
            padding: 2rem;
            border-radius: 12px;
            margin-bottom: 2rem;
        }
        
        .setup-section h3 {
            color: #333;
            margin-bottom: 1rem;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #333;
        }
        
        .form-control {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 1rem;
        }
        
        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #aa00ff, #7200ca);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 5px 15px rgba(170, 0, 255, 0.3);
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
    </style>
</head>
<body>
    <div class="setup-container">
        <div class="setup-header">
            <h1>Admin Setup</h1>
            <p>Set up admin functionality for Achieve CxO</p>
        </div>
        
        <?php if ($message): ?>
            <div class="alert alert-<?php echo $messageType; ?>">
                <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endif; ?>
        
        <div class="setup-section">
            <h3>Step 1: Create Admin Tables</h3>
            <p>This will create the necessary database tables for admin functionality.</p>
            <form method="POST">
                <input type="hidden" name="action" value="setup_tables">
                <button type="submit" class="btn btn-primary">Setup Database Tables</button>
            </form>
        </div>
        
        <div class="setup-section">
            <h3>Step 2: Create Admin User</h3>
            <p>Create your first admin user account.</p>
            <form method="POST">
                <input type="hidden" name="action" value="create_admin">
                
                <div class="form-group">
                    <label for="username">Username</label>
                    <input type="text" id="username" name="username" class="form-control" required>
                </div>
                
                <div class="form-group">
                    <label for="email">Email</label>
                    <input type="email" id="email" name="email" class="form-control" required>
                </div>
                
                <div class="form-group">
                    <label for="full_name">Full Name</label>
                    <input type="text" id="full_name" name="full_name" class="form-control" required>
                </div>
                
                <div class="form-group">
                    <label for="password">Password</label>
                    <input type="password" id="password" name="password" class="form-control" required minlength="6">
                    <small class="text-muted">Minimum 6 characters</small>
                </div>
                
                <button type="submit" class="btn btn-primary">Create Admin User</button>
            </form>
        </div>
        
        <div class="text-center">
            <a href="login.php" class="btn btn-secondary">Go to Admin Login</a>
            <a href="../index.html" class="btn btn-secondary">Back to Main Site</a>
        </div>
    </div>
</body>
</html>
