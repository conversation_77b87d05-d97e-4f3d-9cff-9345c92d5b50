<?php
require_once 'auth.php';

// Set content type to JSON
header('Content-Type: application/json');

// Require authentication
if (!$adminAuth->isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        throw new Exception('Invalid JSON input');
    }
    
    $userId = $input['user_id'] ?? null;
    $newStatus = $input['status'] ?? null;
    
    // Validate input
    if (!$userId || !$newStatus) {
        throw new Exception('User ID and status are required');
    }
    
    // Validate status
    $validStatuses = ['pending', 'active', 'inactive'];
    if (!in_array($newStatus, $validStatuses)) {
        throw new Exception('Invalid status');
    }
    
    // Get database connection
    $pdo = getDatabaseConnection();
    if (!$pdo) {
        throw new Exception('Database connection failed');
    }
    
    // Get current user data
    $stmt = $pdo->prepare("SELECT id, first_name, last_name, email, status FROM cxo_members WHERE id = ?");
    $stmt->execute([$userId]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        throw new Exception('User not found');
    }
    
    $oldStatus = $user['status'];
    
    // Don't update if status is the same
    if ($oldStatus === $newStatus) {
        echo json_encode([
            'success' => true, 
            'message' => 'User status is already ' . $newStatus
        ]);
        exit;
    }
    
    // Update user status
    $stmt = $pdo->prepare("UPDATE cxo_members SET status = ?, updated_at = NOW() WHERE id = ?");
    $stmt->execute([$newStatus, $userId]);
    
    if ($stmt->rowCount() === 0) {
        throw new Exception('Failed to update user status');
    }
    
    // Log admin activity
    $actionMap = [
        'active' => 'approved',
        'inactive' => 'rejected',
        'pending' => 'set_pending'
    ];
    
    $action = $actionMap[$newStatus] ?? 'status_changed';
    $details = sprintf(
        'Changed user status from %s to %s for %s %s (%s)',
        $oldStatus,
        $newStatus,
        $user['first_name'],
        $user['last_name'],
        $user['email']
    );
    
    $adminAuth->logActivity(
        $_SESSION['admin_id'],
        $action,
        'user',
        $userId,
        $details
    );
    
    // Send email notification (optional - you can implement this later)
    // sendStatusChangeEmail($user, $oldStatus, $newStatus);
    
    // Return success response
    $statusMessages = [
        'active' => 'User has been approved and activated',
        'inactive' => 'User has been rejected and deactivated',
        'pending' => 'User status has been set to pending'
    ];
    
    echo json_encode([
        'success' => true,
        'message' => $statusMessages[$newStatus],
        'user_id' => $userId,
        'old_status' => $oldStatus,
        'new_status' => $newStatus
    ]);
    
} catch (Exception $e) {
    error_log("Update user status error: " . $e->getMessage());
    
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

/**
 * Optional: Send email notification when user status changes
 * You can implement this function to send emails to users
 */
function sendStatusChangeEmail($user, $oldStatus, $newStatus) {
    // Implementation depends on your email service
    // This is just a placeholder for future implementation
    
    $subject = '';
    $message = '';
    
    switch ($newStatus) {
        case 'active':
            $subject = 'Welcome to the Achieve CxO Family!';
            $message = sprintf(
                'Dear %s,\n\nCongratulations! Your application has been approved and you are now an active member of the Achieve CxO community.\n\nOne of our members will reach out to you soon to discuss your onboarding and community access.\n\nBest regards,\nThe Achieve CxO Team',
                $user['first_name']
            );
            break;
            
        case 'inactive':
            $subject = 'Achieve CxO Application Update';
            $message = sprintf(
                'Dear %s,\n\nThank you for your interest in joining the Achieve CxO community. After careful review, we are unable to approve your application at this time.\n\nWe encourage you to reapply in the future as your experience and qualifications develop.\n\nBest regards,\nThe Achieve CxO Team',
                $user['first_name']
            );
            break;
    }
    
    if ($subject && $message) {
        // Use your preferred email service here
        // mail($user['email'], $subject, $message);
        // or use PHPMailer, SendGrid, etc.
    }
}
?>
