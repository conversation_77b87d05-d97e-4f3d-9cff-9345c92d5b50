/* Achieve CxO Custom Styles */

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Custom styles for the Join Our Executive Circle section */
#team {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    padding: 80px 0;
}

#team h2 {
    color: #aa00ff;
    font-weight: 600;
    margin-bottom: 2rem;
    font-size: 1.75rem; /* Match About Us section h2 size */
}

#team .text-muted {
    color: #6c757d !important;
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: 2.5rem;
}

/* Form Container */
#joinForm {
    background: #ffffff;
    padding: 2.5rem;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(170, 0, 255, 0.1);
    border: 1px solid rgba(170, 0, 255, 0.1);
    margin-top: 1rem;
}

.form-row {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

/* Form Styles */
.form-group {
    flex: 1;
    position: relative;
}

.form-group input {
    width: 100%;
    padding: 1rem 1.25rem;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    font-size: 1rem;
    font-family: 'Poppins', sans-serif;
    transition: all 0.3s ease;
    background: #ffffff;
    color: #495057;
}

.form-group input:focus {
    outline: none;
    border-color: #aa00ff;
    box-shadow: 0 0 0 0.2rem rgba(170, 0, 255, 0.25);
    transform: translateY(-2px);
}

.form-group input::placeholder {
    color: #adb5bd;
    font-weight: 400;
}

/* Submit Button */
.submit-btn {
    background: linear-gradient(135deg, #aa00ff, #7200ca);
    color: white;
    border: none;
    padding: 1rem 2.5rem;
    border-radius: 50px;
    font-size: 1.1rem;
    font-weight: 600;
    font-family: 'Poppins', sans-serif;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: none;
    letter-spacing: 0.5px;
    box-shadow: 0 4px 15px rgba(170, 0, 255, 0.3);
    width: 100%;
    margin-top: 1rem;
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(170, 0, 255, 0.4);
    background: linear-gradient(135deg, #7200ca, #aa00ff);
}

.submit-btn:active {
    transform: translateY(0);
}

.submit-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

/* Error Message */
.error-message {
    background: #f8d7da;
    color: #721c24;
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    border: 1px solid #f5c6cb;
    display: none;
}

.error-message.show {
    display: block;
    animation: slideInDown 0.3s ease-out;
}

/* Success Message */
.success-message {
    display: none;
    background: linear-gradient(135deg, #c77dff, #9d4edd);
    color: white;
    padding: 2.5rem;
    border-radius: 15px;
    text-align: center;
    margin-top: 1rem;
    box-shadow: 0 10px 30px rgba(199, 125, 255, 0.3);
    position: relative;
    width: 100%;
    box-sizing: border-box;
}

.success-message.show {
    display: block;
    animation: slideInUp 0.5s ease-out;
}

.success-message h3 {
    color: white;
    margin-bottom: 1rem;
    font-size: 1.6rem;
    font-weight: 600;
    white-space: normal;
    word-wrap: break-word;
    line-height: 1.3;
    max-width: 100%;
}

.success-message p {
    color: rgba(255, 255, 255, 0.95);
    font-size: 1.1rem;
    margin: 0 0 1.5rem 0;
    line-height: 1.5;
}

.home-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    margin-top: 1rem;
}

.home-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    color: white;
    text-decoration: none;
    transform: translateY(-1px);
}

.countdown-text {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.8);
    margin-top: 1rem;
}

/* Status-specific styling */
.success-message.pending {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
    box-shadow: 0 10px 30px rgba(255, 193, 7, 0.3);
}

.success-message.active {
    background: linear-gradient(135deg, #28a745, #20c997);
    box-shadow: 0 10px 30px rgba(40, 167, 69, 0.3);
}

/* CTA Button Styling */
.btn-cta {
    background: linear-gradient(135deg, #aa00ff, #7200ca);
    color: white !important;
    border: none;
    padding: 1rem 2rem;
    border-radius: 50px;
    font-size: 1rem;
    font-weight: 600;
    font-family: 'Poppins', sans-serif;
    text-decoration: none !important;
    display: inline-block;
    transition: all 0.3s ease;
    text-transform: none;
    letter-spacing: 0.5px;
    box-shadow: 0 4px 15px rgba(170, 0, 255, 0.3);
}

.btn-cta:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(170, 0, 255, 0.4);
    background: linear-gradient(135deg, #7200ca, #aa00ff);
    color: white !important;
    text-decoration: none !important;
}

.btn-cta:active {
    transform: translateY(0);
}

/* Animations */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* Floating animation for visual appeal */
@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.floating-element {
    animation: float 6s ease-in-out infinite;
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .form-row {
        flex-direction: column;
        gap: 1rem;
    }

    #joinForm {
        padding: 2rem 1.5rem;
        margin: 1rem;
    }

    #team h2 {
        font-size: 2rem;
    }

    .submit-btn {
        padding: 1rem 2rem;
        font-size: 1rem;
    }

    .success-message h3 {
        font-size: 1.4rem;
        line-height: 1.4;
        margin-bottom: 1.2rem;
    }

    .success-message {
        padding: 2rem 1.5rem;
        margin: 1rem 0.5rem;
    }

    .success-message p {
        font-size: 1rem;
    }
}

@media (max-width: 480px) {
    .success-message h3 {
        font-size: 1.25rem;
        line-height: 1.4;
    }

    .success-message {
        padding: 1.5rem 1rem;
        margin: 1rem 0.25rem;
    }

    .success-message p {
        font-size: 0.95rem;
    }
}
