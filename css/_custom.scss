﻿/*!
* Created by <PERSON><PERSON><PERSON> (https://www.kroplet.com) 
* The easiest way to create Bootstrap 4 themes. 
*/

/*Google fonts*/
@import url('https://fonts.googleapis.com/css?family=Poppins:400,600,700|Sacramento');

html,
body {
    width: 100%;
    height: 100%;
    color: #3e396b;
}
body{
    font-family: 'Poppins';
    font-size: 14px;
}
button, input, optgroup, select, textarea{
    font-family: 'Poppins';
}
.launch-text{
    font-family: 'Sacramento';
    font-size: 40px;
    margin-top: 25px;
}
.h1, .h2, .h3, .h4, .h5, .h6, h1, h2, h3, h4, h5, h6{
    font-weight: 600;
}
.brand-logo{
    width: 180px;
}
.hero{
    height: 100vh;
    min-height: 600px;
}
.hero h1{
    font-weight: 100;
    font-size: 4rem;
    margin-bottom: 30px;
}
h1, .h1{
    font-size: 2rem;
}
h2, .h2{
    font-size: 1.75rem;
}
h3, .h3{
    font-size: 1.5rem;
}
h4, .h4{
    font-size: 1.25rem;
}
h5, .h5{
    font-size: 1rem;
}
.hero h1 span{
    font-weight: 700;
} 
a {
    color: #aa00ff;
    -webkit-transition: all .35s;
    -moz-transition: all .35s;
    transition: all .35s;
}
a:hover,
a:focus {
    color: #7200ca !important;
    outline: 0;
    text-decoration: none !important;
}
a.btn:hover,
a.btn:focus {
    color: #fff !important;
}
section{
    padding: 60px 0px;
}
.card-outline-primary{
    border-color: #aa00ff;
}
.col-fixed{
    padding: 20px 0px;
}
@media (min-width:768px){
    .col-fixed{
        position: fixed;
        top: 0;
        bottom: 0;
        padding: 100px 20px 20px 20px;
    }
}
.form-control{
    height: 45px;
}
.form-control:hover, .form-control:focus{
    border-color: #aa00ff;
}
.img-team{
    width: 80px;
}
/*===============================================
* Background Colors
================================================*/
.bg-alt {
    background-color: #fff;
}
.bg-faded{
    background-color: #F2F2F2;
}
.bg-blue{
    background-color: #032EFF !important;
}
.bg-primary{
    background-color: #aa00ff !important;
}
.bg-purple{
    background-color: #3d5afe;
}
.bg-orange{
    background-color: #ffea00;
}
.bg-yellow{
    background-color:#feac00;
}
.bg-pink{
    background-color:#ff0080;
}
.bg-green{
    background-color:#aa00ff;
}
.bg-red{
    background-color:#FF3B30;
}
.bg-violet{
    background-color: #7B1FA2;
}
.bg-teal{
    background-color: #00796B;
}
.bg-slate{
    background-color: #455A64;
}
.bg-indigo{
    background-color: #303F9F;
}
/*===============================================
* Text Colors
================================================*/
.text-faded {
    color: #D9EDF7;
}
.text-dark {
    color: #37474F;
}
.text-muted{
    color: #999 !important;
}
.text-light {
    color: #fff;
}
.text-light:hover,.text-light:focus {
    color: #eee;
    text-decoration:none;
}
.text-primary {
    color: #aa00ff !important;
}
.text-purple{
    color: #3d5afe !important;
}
.text-orange{
    color: #ffea00 !important;
}
.text-yellow{
    color:#feac00 !important;
}
.text-pink{
    color:#ff0080 !important;
}
.text-green{
    color:#aa00ff !important;
}
.text-red{
    color:#FF3B30 !important;
}
.text-violet{
    color: #7B1FA2 !important;
}
.text-teal{
    color: #00796B !important;
}
.text-slate{
    color: #455A64 !important;
}
.text-indigo{
    color: #303F9F !important;
}
/*===============================================
* Icon Sizes
================================================*/
.icon-lg {
    font-size: 60px;
    line-height: 18px;
}
.icon-md {
    font-size: 50px;
    line-height: 14px;
}
.icon-sm {
    font-size: 30px;
    line-height: 14px;
}
/*===============================================
* Colored Buttons
================================================*/
.btn-outline-white {
    color: #fff !important;
    background-image: none;
    background-color: transparent;
    border-color: #fff;
}
.btn-outline-white:hover {
    background: rgba(255, 255, 255, 0.2) !important;
}
.btn-white {
    color: #aa00ff;
    background-color: #fff;
    border-color: #fff;
}
.btn-white:hover {
    background: transparent !important;
    color: #fff !important;
}
.btn-radius{
    border-radius: 50px;
}
.border-none{
    border: none !important;
    border-color: transparent !important;
}

/*social icons*/
.text-twitter-alt, .text-facebook-alt, .text-linkedin-alt, .text-google-alt, .text-github-alt{
    color:#aa00ff;
}
.text-twitter, .text-twitter-alt:hover{
    color:#00aced;
}
.text-facebook, .text-facebook-alt:hover{
    color: #3b5998;
}
.text-google, .text-google-alt:hover{
    color: #dd4b39;
}
.text-linkedin, .text-linkedin-alt:hover{
    color: #007bb6;
}
.text-github, .text-github-alt:hover{
    color: #000000;
}