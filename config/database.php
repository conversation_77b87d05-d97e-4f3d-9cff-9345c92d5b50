<?php
/**
 * Database Configuration for Achieve CxO
 */

// Database configuration
define('DB_HOST', 'localhost');
define('DB_NAME', 'u548077102_achievecxo');
define('DB_USER', 'u548077102_cxoadmin');     // Change this to your database username
define('DB_PASS', 'Yx:O5JL2X?5');     // Change this to your database password
define('DB_CHARSET', 'utf8mb4');

/**
 * Create database connection
 * @return PDO|null
 */
function getDatabaseConnection() {
    try {
        $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
        $options = [
            PDO::ATTR_ERRMODE            => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES   => false,
        ];
        
        $pdo = new PDO($dsn, DB_USER, DB_PASS, $options);
        return $pdo;
    } catch (PDOException $e) {
        error_log("Database connection failed: " . $e->getMessage());
        return null;
    }
}

/**
 * Test database connection
 * @return bool
 */
function testDatabaseConnection() {
    $pdo = getDatabaseConnection();
    return $pdo !== null;
}
?>
