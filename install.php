<?php
/**
 * Database Installation Script for Achieve CxO
 * Run this file once to set up the database
 */

// Include database configuration
require_once 'config/database.php';

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Achieve CxO - Database Setup</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; }
        .success { color: #28a745; background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .info { color: #0c5460; background: #d1ecf1; padding: 15px; border-radius: 5px; margin: 10px 0; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Achieve CxO - Database Setup</h1>
    
    <?php
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        try {
            // Read SQL file
            $sqlFile = 'database/setup.sql';
            if (!file_exists($sqlFile)) {
                throw new Exception("SQL file not found: $sqlFile");
            }
            
            $sql = file_get_contents($sqlFile);
            if (!$sql) {
                throw new Exception("Could not read SQL file");
            }
            
            // Create connection without database name first
            $dsn = "mysql:host=" . DB_HOST . ";charset=" . DB_CHARSET;
            $pdo = new PDO($dsn, DB_USER, DB_PASS, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
            ]);
            
            // Execute SQL statements
            $statements = explode(';', $sql);
            foreach ($statements as $statement) {
                $statement = trim($statement);
                if (!empty($statement)) {
                    $pdo->exec($statement);
                }
            }
            
            echo '<div class="success">✅ Database setup completed successfully!</div>';
            echo '<div class="info">The cxo_members table has been created and is ready to use.</div>';
            
        } catch (Exception $e) {
            echo '<div class="error">❌ Error: ' . htmlspecialchars($e->getMessage()) . '</div>';
        }
    }
    ?>
    
    <div class="info">
        <h3>Before running the installation:</h3>
        <ol>
            <li>Make sure MySQL/MariaDB is running</li>
            <li>Update database credentials in <code>config/database.php</code></li>
            <li>Ensure your database user has CREATE privileges</li>
        </ol>
    </div>
    
    <form method="POST">
        <button type="submit" style="background: #aa00ff; color: white; padding: 15px 30px; border: none; border-radius: 5px; font-size: 16px; cursor: pointer;">
            Install Database
        </button>
    </form>
    
    <h3>Database Configuration</h3>
    <pre><?php
    echo "Host: " . DB_HOST . "\n";
    echo "Database: " . DB_NAME . "\n";
    echo "Username: " . DB_USER . "\n";
    echo "Password: " . (DB_PASS ? str_repeat('*', strlen(DB_PASS)) : 'Not set') . "\n";
    ?></pre>
    
    <h3>Table Structure</h3>
    <pre>
CREATE TABLE cxo_members (
    id              INT(11) AUTO_INCREMENT PRIMARY KEY,
    first_name      VARCHAR(100) NOT NULL,
    last_name       VARCHAR(100) NOT NULL,
    email           VARCHAR(255) NOT NULL UNIQUE,
    created_at      TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at      TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    status          ENUM('pending','active','inactive') DEFAULT 'pending',
    
    INDEX idx_created_at (created_at),
    INDEX idx_status (status)
);
    </pre>
</body>
</html>
