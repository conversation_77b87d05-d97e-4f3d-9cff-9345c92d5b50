<?php
/**
 * Form submission handler for Achieve CxO Executive Circle
 */

// Set content type to JSON
header('Content-Type: application/json');

// Enable CORS if needed
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

// Include database configuration
require_once 'config/database.php';

try {
    // Get JSON input
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);
    
    // Validate input
    if (!$data) {
        throw new Exception('Invalid JSON data');
    }
    
    // Validate required fields
    $firstName = trim($data['firstName'] ?? '');
    $lastName = trim($data['lastName'] ?? '');
    $email = trim($data['email'] ?? '');
    
    if (empty($firstName)) {
        throw new Exception('First name is required');
    }
    
    if (empty($lastName)) {
        throw new Exception('Last name is required');
    }
    
    if (empty($email)) {
        throw new Exception('Email is required');
    }
    
    // Validate email format
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        throw new Exception('Please enter a valid email address');
    }
    
    // Get database connection
    $pdo = getDatabaseConnection();
    if (!$pdo) {
        throw new Exception('Database connection failed');
    }
    
    // Check if email already exists
    $stmt = $pdo->prepare("SELECT id, status, first_name FROM cxo_members WHERE email = ?");
    $stmt->execute([$email]);
    $existingUser = $stmt->fetch();

    if ($existingUser) {
        // User already exists, return status-based message
        $status = $existingUser['status'];
        $name = $existingUser['first_name'];

        switch($status) {
            case 'pending':
                $message = "Thanks for your continued interest! We already have your application under review and will be in touch shortly.";
                break;
            case 'active':
                $message = "Congratulations, you\'re in. One of our members will reach out shortly to discuss your onboarding and community access.";
                break;
            case 'inactive':
                $message = "Your account is currently inactive. Please contact support for assistance.";
                break;
            default:
                $message = "Thank you for your interest. We\'ll be in touch soon with exclusive access details.";
        }

        echo json_encode([
            'success' => true,
            'status' => 'existing',
            'user_status' => $status,
            'message' => $message
        ]);
        exit;
    }
    
    // Insert new member
    $stmt = $pdo->prepare("
        INSERT INTO cxo_members (first_name, last_name, email, status) 
        VALUES (?, ?, ?, 'pending')
    ");
    
    $result = $stmt->execute([$firstName, $lastName, $email]);
    
    if (!$result) {
        throw new Exception('Failed to save your information');
    }
    
    // Log successful registration
    error_log("New CxO member registered: $firstName $lastName ($email)");
    
    // Send success response
    echo json_encode([
        'success' => true,
        'status' => 'new',
        'message' => 'Thank you for your interest! We will be in touch soon with exclusive access details.'
    ]);
    
} catch (Exception $e) {
    // Log error
    error_log("CxO registration error: " . $e->getMessage());
    
    // Send error response
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
