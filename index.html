<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="utf-8">
        <title>Achieve CxO - Executive Leadership Community</title>
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <!--Bootstrap 4-->
        <link rel="stylesheet" href="css/bootstrap.min.css">
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/3.5.2/animate.min.css">
        <!--icons-->
        <link rel="stylesheet" href="https://code.ionicframework.com/ionicons/2.0.1/css/ionicons.min.css" />
        <link rel="stylesheet" href="js/countdown/jquery.countdown.css" />
    </head>
    <body>
        <!--hero section-->
        <section class="bg-white hero p-0">
            <div class="container-fluid">
                <div class="row">
                    <div class="col-sm-5 bg-light text-center col-fixed">
                        <h1 class="pt-4 h2"><span class="text-green">Achieve CxO.</span></h1>
                        <h4>Where Executive Excellence Meets Authentic Connection.</h4>
                        <p class="mt-4">
                            <br/>
                        </p>
                        <div class="launch-text">We're launching soon.</div>
                        <ul id="timer">
                            <li><span class="days">00</span><p class="days_text">Days</p></li>
                            <li class="seperator">:</li>
                            <li><span class="hours">00</span><p class="hours_text">Hours</p></li>
                            <li class="seperator">:</li>
                            <li><span class="minutes">00</span><p class="minutes_text">Minutes</p></li>
                            <li class="seperator">:</li>
                            <li><span class="seconds">00</span><p class="seconds_text">Seconds</p></li>
                        </ul>
                        <div class="mt-4">
                            <a href="#team" class="btn btn-cta">Join our Executive Circle</a>
                        </div>
                    </div>
                    <div class="col-sm-7 offset-sm-5 px-0 py-5">
                        <!--Why This Domain-->
                        <section class="pt-4">
                            <div class="container-fluid">
                                <div class="row">
                                    <div class="col-sm-10 mx-auto text-center">
                                        <h2 class="text-primary pb-3">About Us</h2>
                                        <p class="mt-4">
                                            We are a family of leaders who support, challenge, and celebrate each other's journey to excellence.  In our member-owned community, every voice shapes the future. Together, we forge meaningful connections, unlock unprecedented opportunities, and create lasting impact that transcends traditional networking.
                                        </p>
                                    </div>
                                </div>
                                <div class="row d-md-flex mt-4">
                                    <div class="col-sm-4 mt-2 text-center">
                                        <p><em class="ion-ios-people icon-md text-primary"></em></p>
                                        <h4>Peer Support</h4>
                                        <p class="text-muted">Connect with fellow executives who understand your challenges.</p>
                                    </div>
                                    <div class="col-sm-4 mt-2 text-center">
                                        <p><em class="ion-ios-analytics icon-md text-primary"></em></p>
                                        <h4>Growth Opportunities</h4>
                                        <p class="text-muted">Access exclusive opportunities, events, insights, and strategic partnerships.</p>
                                    </div>
                                    <div class="col-sm-4 mt-2 text-center">
                                        <p><em class="ion-ios-pulse icon-md text-primary"></em></p>
                                        <h4>Collective Impact</h4>
                                        <p class="text-muted">Shape the future of leadership in your member-owned community.</p>
                                    </div>
                                </div>
                            </div>
                        </section>

                        <!--team-->
                        <section id="team">
                            <div class="container-fluid">
                                <div class="row">
                                    <div class="col-sm-10 mx-auto text-center">
                                        <h2 class="text-primary pb-3">Join Our Executive Circle</h2>
                                        <p class="mt-4">
                                            <!-- Join our exclusive community of executive leaders. Request early access to our platform and be among the first to experience the transformative power of Achieve CxO.-->
                                             An invitation-only sanctuary for visionary executives. Request early access to our platform and be among the first to experience the transformative power of Achieve CxO.
                                        </p>
                                    </div>
                                </div>
                                <div class="row d-md-flex mt-4">
                                    <div class="col-sm-8 mx-auto">
                                        <form id="joinForm" class="floating-element">
                                            <div class="form-row">
                                                <div class="form-group">
                                                    <input type="text" id="firstName" name="firstName" placeholder="First Name" required>
                                                </div>
                                                <div class="form-group">
                                                    <input type="text" id="lastName" name="lastName" placeholder="Last Name" required>
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <input type="email" id="email" name="email" placeholder="Executive Email Address" required>
                                            </div>
                                            <button type="submit" class="submit-btn">
                                                Request Exclusive Access
                                            </button>
                                        </form>
                                        <div id="successMessage" class="success-message">
                                            <h3>Welcome to the Achieve CxO Family!</h3>
                                            <p>Thank you for your interest. We'll be in touch soon with exclusive access details.</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </section>
                        <!--footer-->
                        <section class="mt-2">
                            <div class="container">
                                <div class="row mr-5 ml-5">
                                    <div class="col-sm-12">
                                        <p class="text-muted text-center">
                                            &copy; 2025 Achieve CxO - All Rights Reserved
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </section>
                    </div>
                </div>
            </div>
        </section>
    
    
    <style>
        /* Custom styles for the Join Our Executive Circle section */
        #team {
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            padding: 80px 0;
        }

        #team h2 {
            color: #aa00ff;
            font-weight: 600;
            margin-bottom: 2rem;
            font-size: 1.75rem; /* Match About Us section h2 size */
        }

        #team .text-muted {
            color: #6c757d !important;
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 2.5rem;
        }

        #joinForm {
            background: #ffffff;
            padding: 2.5rem;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(170, 0, 255, 0.1);
            border: 1px solid rgba(170, 0, 255, 0.1);
            margin-top: 1rem;
        }

        .form-row {
            display: flex;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .form-group {
            flex: 1;
            position: relative;
        }

        .form-group input {
            width: 100%;
            padding: 1rem 1.25rem;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            font-size: 1rem;
            font-family: 'Poppins', sans-serif;
            transition: all 0.3s ease;
            background: #ffffff;
            color: #495057;
        }

        .form-group input:focus {
            outline: none;
            border-color: #aa00ff;
            box-shadow: 0 0 0 0.2rem rgba(170, 0, 255, 0.25);
            transform: translateY(-2px);
        }

        .form-group input::placeholder {
            color: #adb5bd;
            font-weight: 400;
        }

        .submit-btn {
            background: linear-gradient(135deg, #aa00ff, #7200ca);
            color: white;
            border: none;
            padding: 1rem 2.5rem;
            border-radius: 50px;
            font-size: 1.1rem;
            font-weight: 600;
            font-family: 'Poppins', sans-serif;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: none;
            letter-spacing: 0.5px;
            box-shadow: 0 4px 15px rgba(170, 0, 255, 0.3);
            width: 100%;
            margin-top: 1rem;
        }

        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(170, 0, 255, 0.4);
            background: linear-gradient(135deg, #7200ca, #aa00ff);
        }

        .submit-btn:active {
            transform: translateY(0);
        }

        .submit-btn:disabled {
            opacity: 0.7;
            cursor: not-allowed;
            transform: none;
        }

        .success-message {
            display: none;
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 2.5rem;
            border-radius: 15px;
            text-align: center;
            margin-top: 1rem;
            box-shadow: 0 10px 30px rgba(40, 167, 69, 0.2);
        }

        .success-message.show {
            display: block;
            animation: slideInUp 0.5s ease-out;
        }

        .success-message h3 {
            color: white;
            margin-bottom: 1rem;
            font-size: 1.75rem;
            font-weight: 600;
        }

        .success-message p {
            color: rgba(255, 255, 255, 0.9);
            font-size: 1.1rem;
            margin: 0;
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .form-row {
                flex-direction: column;
                gap: 1rem;
            }

            #joinForm {
                padding: 2rem 1.5rem;
                margin: 1rem;
            }

            #team h2 {
                font-size: 2rem;
            }

            .submit-btn {
                padding: 1rem 2rem;
                font-size: 1rem;
            }
        }

        /* Floating animation for visual appeal */
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .floating-element {
            animation: float 6s ease-in-out infinite;
        }

        /* CTA Button Styling */
        .btn-cta {
            background: linear-gradient(135deg, #aa00ff, #7200ca);
            color: white !important;
            border: none;
            padding: 1rem 2rem;
            border-radius: 50px;
            font-size: 1rem;
            font-weight: 600;
            font-family: 'Poppins', sans-serif;
            text-decoration: none !important;
            display: inline-block;
            transition: all 0.3s ease;
            text-transform: none;
            letter-spacing: 0.5px;
            box-shadow: 0 4px 15px rgba(170, 0, 255, 0.3);
        }

        .btn-cta:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(170, 0, 255, 0.4);
            background: linear-gradient(135deg, #7200ca, #aa00ff);
            color: white !important;
            text-decoration: none !important;
        }

        .btn-cta:active {
            transform: translateY(0);
        }

        /* Smooth scrolling */
        html {
            scroll-behavior: smooth;
        }
    </style>

    <script>
        // Form submission handling
        const form = document.getElementById('joinForm');
        const successMessage = document.getElementById('successMessage');

        form.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const firstName = document.getElementById('firstName').value.trim();
            const lastName = document.getElementById('lastName').value.trim();
            const email = document.getElementById('email').value.trim();

            // Basic validation
            if (!firstName || !lastName || !email) {
                showError('Please fill in all fields');
                return;
            }

            if (!isValidEmail(email)) {
                showError('Please enter a valid email address');
                return;
            }

            // Show loading state
            const submitBtn = document.querySelector('.submit-btn');
            const originalText = submitBtn.textContent;
            submitBtn.textContent = 'Submitting...';
            submitBtn.disabled = true;

            // Submit to PHP backend
            fetch('submit.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    firstName: firstName,
                    lastName: lastName,
                    email: email
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Hide form and show success message
                    form.style.display = 'none';
                    successMessage.classList.add('show');
                } else {
                    showError(data.message || 'Something went wrong. Please try again.');
                    submitBtn.textContent = originalText;
                    submitBtn.disabled = false;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showError('Network error. Please check your connection and try again.');
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
            });
        });

        function showError(message) {
            // Create or update error message
            let errorDiv = document.getElementById('errorMessage');
            if (!errorDiv) {
                errorDiv = document.createElement('div');
                errorDiv.id = 'errorMessage';
                errorDiv.style.cssText = `
                    background: linear-gradient(135deg, #f56565, #e53e3e);
                    color: white;
                    padding: 1rem;
                    border-radius: 12px;
                    margin-bottom: 1rem;
                    text-align: center;
                    animation: slideInUp 0.3s ease-out;
                `;
                form.insertBefore(errorDiv, form.firstChild);
            }
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
            
            // Hide error after 5 seconds
            setTimeout(() => {
                errorDiv.style.display = 'none';
            }, 5000);
        }

        function isValidEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }

        // Add smooth scrolling and interactive effects
        document.querySelectorAll('input').forEach(input => {
            input.addEventListener('focus', function() {
                this.parentElement.style.transform = 'scale(1.02)';
            });
            
            input.addEventListener('blur', function() {
                this.parentElement.style.transform = 'scale(1)';
            });
        });

        // Parallax effect for floating elements
        document.addEventListener('mousemove', function(e) {
            const elements = document.querySelectorAll('.floating-element');
            const x = e.clientX / window.innerWidth;
            const y = e.clientY / window.innerHeight;
            
            elements.forEach((element, index) => {
                const speed = (index + 1) * 0.5;
                const xPos = (x - 0.5) * speed * 50;
                const yPos = (y - 0.5) * speed * 50;
                element.style.transform += ` translate(${xPos}px, ${yPos}px)`;
            });
        });
    </script>
        
        <script src="https://code.jquery.com/jquery-3.1.1.min.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.12.3/umd/popper.min.js"></script>
        <script src="//maxcdn.bootstrapcdn.com/bootstrap/4.0.0-beta.2/js/bootstrap.min.js"></script>
        <script src="js/countdown/jquery.countdown.min.js"></script>
        <script src="js/scripts.js"></script>
    </body>
</html>
