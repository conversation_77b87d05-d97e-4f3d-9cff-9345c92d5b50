<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="utf-8">
        <title>Achieve CxO - Executive Leadership Community</title>
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <!--Bootstrap 4-->
        <link rel="stylesheet" href="css/bootstrap.min.css">
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/3.5.2/animate.min.css">
        <!--icons-->
        <link rel="stylesheet" href="https://code.ionicframework.com/ionicons/2.0.1/css/ionicons.min.css" />
        <link rel="stylesheet" href="js/countdown/jquery.countdown.css" />
        <!-- Custom Styles -->
        <link rel="stylesheet" href="css/styles.css">
    </head>
    <body>
        <!--hero section-->
        <section class="bg-white hero p-0">
            <div class="container-fluid">
                <div class="row">
                    <div class="col-sm-5 bg-light text-center col-fixed">
                        <h1 class="pt-4 h2"><span class="text-green">Achieve CxO.</span></h1>
                        <h4>Where Executive Excellence Meets Authentic Connection.</h4>
                        <p class="mt-4">
                            <br/>
                        </p>
                       
                        <div class="mt-4">
                            <a href="#team" class="btn btn-cta">Join our Executive Circle</a>
                        </div>

                        <!-- Discreet admin link -->
                        <div class="mt-3">
                            <a href="admin/" style="color: #6c757d; font-size: 0.8rem; text-decoration: none;">Admin</a>
                        </div>
                    </div>
                    <div class="col-sm-7 offset-sm-5 px-0 py-5">
                        <!--Why This Domain-->
                        <section class="pt-4">
                            <div class="container-fluid">
                                <div class="row">
                                    <div class="col-sm-10 mx-auto text-center">
                                        <h2 class="text-primary pb-3">About Us</h2>
                                        <p class="mt-4">
                                            We are a family of leaders who support, challenge, and celebrate each other's journey to excellence.  In our member-owned community, every voice shapes the future. Together, we forge meaningful connections, unlock unprecedented opportunities, and create lasting impact that transcends traditional networking.
                                        </p>
                                    </div>
                                </div>
                                <div class="row d-md-flex mt-4">
                                    <div class="col-sm-4 mt-2 text-center">
                                        <p><em class="ion-ios-people icon-md text-primary"></em></p>
                                        <h4>Peer Support</h4>
                                        <p class="text-muted">Connect with fellow executives who understand your challenges.</p>
                                    </div>
                                    <div class="col-sm-4 mt-2 text-center">
                                        <p><em class="ion-ios-analytics icon-md text-primary"></em></p>
                                        <h4>Growth Opportunities</h4>
                                        <p class="text-muted">Access exclusive opportunities, events, insights, and strategic partnerships.</p>
                                    </div>
                                    <div class="col-sm-4 mt-2 text-center">
                                        <p><em class="ion-ios-pulse icon-md text-primary"></em></p>
                                        <h4>Collective Impact</h4>
                                        <p class="text-muted">Shape the future of leadership in your member-owned community.</p>
                                    </div>
                                </div>
                            </div>
                        </section>

                        <!--team-->
                        <section id="team">
                            <div class="container-fluid">
                                <div class="row">
                                    <div class="col-sm-10 mx-auto text-center">
                                        <h2 class="text-primary pb-3">Join Our Executive Circle</h2>
                                        <p class="mt-4">
                                            <!-- Join our exclusive community of executive leaders. Request early access to our platform and be among the first to experience the transformative power of Achieve CxO.-->
                                                An invitation-only sanctuary for visionary executives. Request access to join leaders who challenge, support, and celebrate each other.
                                        </p>
                                    </div>
                                </div>
                                <div class="row d-md-flex mt-4">
                                    <div class="col-sm-8 mx-auto">
                                        <form id="joinForm" class="floating-element">
                                            <div class="form-row">
                                                <div class="form-group">
                                                    <input type="text" id="firstName" name="firstName" placeholder="First Name" required>
                                                </div>
                                                <div class="form-group">
                                                    <input type="text" id="lastName" name="lastName" placeholder="Last Name" required>
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <input type="email" id="email" name="email" placeholder="Executive Email Address" required>
                                            </div>
                                            <button type="submit" class="submit-btn">
                                                Request Exclusive Access
                                            </button>
                                        </form>
                                        <div id="successMessage" class="success-message">
                                            <h3 id="successTitle">Welcome to the Achieve CxO Family!</h3>
                                            <p id="successText">You're now on the list. Our membership curator will personally reach out to discuss your exclusive access and next steps.</p>
                                            <a href="#" id="homeBtn" class="home-btn">Return to Home</a>
                                            <div id="countdownText" class="countdown-text"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </section>
                        <!--footer-->
                        <section class="mt-2">
                            <div class="container">
                                <div class="row mr-5 ml-5">
                                    <div class="col-sm-12">
                                        <p class="text-muted text-center">
                                            &copy; 2025 Achieve CxO - All Rights Reserved
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </section>
                    </div>
                </div>
            </div>
        </section>
    
    <script>
        // Form submission handling
        const form = document.getElementById('joinForm');
        const successMessage = document.getElementById('successMessage');

        form.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const firstName = document.getElementById('firstName').value.trim();
            const lastName = document.getElementById('lastName').value.trim();
            const email = document.getElementById('email').value.trim();

            // Basic validation
            if (!firstName || !lastName || !email) {
                showError('Please fill in all fields');
                return;
            }

            if (!isValidEmail(email)) {
                showError('Please enter a valid email address');
                return;
            }

            // Show loading state
            const submitBtn = document.querySelector('.submit-btn');
            const originalText = submitBtn.textContent;
            submitBtn.textContent = 'Submitting...';
            submitBtn.disabled = true;

            // Submit to PHP backend
            fetch('submit.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    firstName: firstName,
                    lastName: lastName,
                    email: email
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Hide form and show success message
                    form.style.display = 'none';

                    // Update message based on status
                    updateSuccessMessage(data.status || 'new', data.message);

                    successMessage.classList.add('show');

                    // Start countdown and auto-redirect
                    startAutoRedirect();
                } else {
                    showError(data.message || 'Something went wrong. Please try again.');
                    submitBtn.textContent = originalText;
                    submitBtn.disabled = false;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showError('Network error. Please check your connection and try again.');
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
            });
        });

        function showError(message) {
            // Create or update error message
            let errorDiv = document.getElementById('errorMessage');
            if (!errorDiv) {
                errorDiv = document.createElement('div');
                errorDiv.id = 'errorMessage';
                errorDiv.style.cssText = `
                    background: linear-gradient(135deg, #f56565, #e53e3e);
                    color: white;
                    padding: 1rem;
                    border-radius: 12px;
                    margin-bottom: 1rem;
                    text-align: center;
                    animation: slideInUp 0.3s ease-out;
                `;
                form.insertBefore(errorDiv, form.firstChild);
            }
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
            
            // Hide error after 5 seconds
            setTimeout(() => {
                errorDiv.style.display = 'none';
            }, 5000);
        }

        function isValidEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }

        function updateSuccessMessage(status, customMessage) {
            const titleElement = document.getElementById('successTitle');
            const textElement = document.getElementById('successText');
            const messageContainer = document.getElementById('successMessage');

            // Remove existing status classes
            messageContainer.classList.remove('pending', 'active');

            switch(status) {
                case 'pending':
                    titleElement.textContent = 'Application Under Review';
                    textElement.textContent = customMessage || 'Thank you for your interest in joining our leadership community. We\'re personally reviewing your application and will be in touch shortly.';
                    messageContainer.classList.add('pending');
                    break;

                case 'active':
                    titleElement.textContent = 'Welcome to the Family!';
                    textElement.textContent = customMessage || 'Congratulations, you\'re in. One of our members will reach out shortly to discuss your onboarding and community access.';
                    messageContainer.classList.add('active');
                    break;

                case 'existing':
                    if (customMessage && customMessage.includes('pending')) {
                        titleElement.textContent = 'Application Under Review';
                        textElement.textContent = 'Thank you for your interest in joining our leadership community. We\'re personally reviewing your application and will be in touch shortly.';
                        messageContainer.classList.add('pending');
                    } else if (customMessage && customMessage.includes('active')) {
                        titleElement.textContent = 'Welcome to the Family!';
                        textElement.textContent = 'Congratulations, you\'re in. One of our members will reach out shortly to discuss your onboarding and community access.';
                        messageContainer.classList.add('active');
                    } else {
                        titleElement.textContent = 'Already Registered';
                        textElement.textContent = customMessage || 'Thanks for your continued interest! We already have your application under review and will be in touch soon.';
                    }
                    break;

                default:
                    titleElement.textContent = 'Welcome to the Achieve CxO Family!';
                    textElement.textContent = customMessage || 'Thank you for your interest. We\'ll be in touch soon with exclusive access details.';
                    break;
            }
        }

        function startAutoRedirect() {
            let countdown = 7; // 7 seconds
            const countdownElement = document.getElementById('countdownText');
            const homeBtn = document.getElementById('homeBtn');

            // Update countdown display
            function updateCountdown() {
                countdownElement.textContent = `Redirecting to home in ${countdown} seconds...`;
                countdown--;

                if (countdown < 0) {
                    redirectToHome();
                } else {
                    setTimeout(updateCountdown, 1000);
                }
            }

            // Start countdown
            updateCountdown();

            // Add click handler for home button
            homeBtn.addEventListener('click', function(e) {
                e.preventDefault();
                redirectToHome();
            });
        }

        function redirectToHome() {
            // Scroll to top and refresh the page
            window.scrollTo({ top: 0, behavior: 'smooth' });

            setTimeout(() => {
                // Reset form and hide success message
                document.getElementById('joinForm').style.display = 'block';
                document.getElementById('successMessage').classList.remove('show');

                // Reset form fields
                document.getElementById('firstName').value = '';
                document.getElementById('lastName').value = '';
                document.getElementById('email').value = '';

                // Re-enable submit button
                const submitBtn = document.querySelector('.submit-btn');
                submitBtn.textContent = 'Request Exclusive Access';
                submitBtn.disabled = false;
            }, 500);
        }

        // Add smooth scrolling and interactive effects
        document.querySelectorAll('input').forEach(input => {
            input.addEventListener('focus', function() {
                this.parentElement.style.transform = 'scale(1.02)';
            });
            
            input.addEventListener('blur', function() {
                this.parentElement.style.transform = 'scale(1)';
            });
        });

        // Parallax effect for floating elements
        document.addEventListener('mousemove', function(e) {
            const elements = document.querySelectorAll('.floating-element');
            const x = e.clientX / window.innerWidth;
            const y = e.clientY / window.innerHeight;
            
            elements.forEach((element, index) => {
                const speed = (index + 1) * 0.5;
                const xPos = (x - 0.5) * speed * 50;
                const yPos = (y - 0.5) * speed * 50;
                element.style.transform += ` translate(${xPos}px, ${yPos}px)`;
            });
        });
    </script>
        
        <script src="https://code.jquery.com/jquery-3.1.1.min.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.12.3/umd/popper.min.js"></script>
        <script src="//maxcdn.bootstrapcdn.com/bootstrap/4.0.0-beta.2/js/bootstrap.min.js"></script>
        <script src="js/countdown/jquery.countdown.min.js"></script>
        <script src="js/scripts.js"></script>
    </body>
</html>
